{
    "data": [
        {
            "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a",
            "name": "Google Document",
            "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/1750839188-Google-Docs-logo.png",
            "description": "Google Document MCP Server.",
            "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://google-docs-mcp-dev-624209391722.us-central1.run.app/mcp",
                    "type": "streamable-http"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": null,
            "status": "active",
            "created_at": "2025-06-24T13:00:48.696889",
            "updated_at": "2025-07-23T09:38:38.583242",
            "image_name": null,
            "category": "general",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "update_document",
                        "description": "Update a Google Document with new content at a specific position",
                        "input_schema": {
                            "properties": {
                                "document_id": {
                                    "title": "Document Id",
                                    "type": "string"
                                },
                                "content": {
                                    "title": "Content",
                                    "type": "string"
                                },
                                "insert_at": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Insert At"
                                }
                            },
                            "required": [
                                "document_id",
                                "content"
                            ],
                            "title": "UpdateDocument",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "verify_connection",
                        "description": "Verify the connection to Google Docs API and display user info",
                        "input_schema": {
                            "properties": {},
                            "title": "VerifyConnection",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "append_document",
                        "description": "Append content to the end of a Google Document",
                        "input_schema": {
                            "properties": {
                                "document_id": {
                                    "title": "Document Id",
                                    "type": "string"
                                },
                                "content": {
                                    "title": "Content",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "document_id",
                                "content"
                            ],
                            "title": "AppendDocument",
                            "type": "object"
                        },
                        "output_schema": {
                            "profile": ""
                        },
                        "annotations": null
                    },
                    {
                        "name": "insert_image",
                        "description": "Insert an image into a Google Document at a specific position",
                        "input_schema": {
                            "properties": {
                                "document_id": {
                                    "title": "Document Id",
                                    "type": "string"
                                },
                                "image_url": {
                                    "title": "Image Url",
                                    "type": "string"
                                },
                                "insert_index": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Insert Index"
                                },
                                "width": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 300,
                                    "title": "Width"
                                },
                                "height": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 200,
                                    "title": "Height"
                                },
                                "alt_text": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Alt Text"
                                }
                            },
                            "required": [
                                "document_id",
                                "image_url"
                            ],
                            "title": "InsertImage",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "create_document",
                        "description": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.",
                        "input_schema": {
                            "properties": {
                                "title": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Title"
                                },
                                "content": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Content"
                                },
                                "format": {
                                    "anyOf": [
                                        {
                                            "enum": [
                                                "plain",
                                                "html",
                                                "markdown"
                                            ],
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "plain",
                                    "title": "Format"
                                }
                            },
                            "title": "CreateDocument",
                            "type": "object"
                        },
                        "output_schema": {},
                        "annotations": null
                    },
                    {
                        "name": "get_document",
                        "description": "Retrieve the content of a Google Document by its ID",
                        "input_schema": {
                            "properties": {
                                "document_id": {
                                    "title": "Document Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "document_id"
                            ],
                            "title": "GetDocument",
                            "type": "object"
                        },
                        "output_schema": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": "{\n  \"success\": true,\n  \"message\": \"Image inserted successfully\",\n  \"document_id\": \"1NiM26Hn0HqLy49Xc_yyCt5mUF8hrzAIE5rbMpGZzSK4\",\n  \"image_url\": \"https://images.pexels.com/photos/1566308/pexels-photo-1566308.jpeg\",\n  \"insert_index\": 20,\n  \"width\": 300,\n  \"height\": 200,\n  \"alt_text\": null,\n  \"url\": \"https://docs.google.com/document/d/1NiM26Hn0HqLy49Xc_yyCt5mUF8hrzAIE5rbMpGZzSK4/edit\"\n}"
                                }
                            ],
                            "isError": false
                        },
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "file_handling",
            "env_credential_status": "pending_input",
            "oauth_details": {
                "provider": "google",
                "tool_name": "google_document"
            }
        }
    ],
    "metadata": {
        "total": 1,
        "totalPages": 1,
        "currentPage": 1,
        "pageSize": 10,
        "hasNextPage": false,
        "hasPreviousPage": false
    }
}

{
    "data": [
        {
            "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce",
            "name": "MetaAds",
            "logo": null,
            "description": "MCP server acting as an interface to the Facebook Ads, enabling programmatic access to Facebook Ads data and management features.",
            "owner_id": "5229e05b-aaea-4850-9972-7268559318cf",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://meta-mcp-dev-624209391722.us-central1.run.app/mcp",
                    "type": "streamable-http"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": [
                "ads"
            ],
            "status": "active",
            "created_at": "2025-07-10T06:58:12.782791",
            "updated_at": "2025-07-22T14:29:44.379915",
            "image_name": null,
            "category": "marketing",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "create_meta_campaign",
                        "description": "Create a Meta Ads campaign with specified configuration",
                        "input_schema": {
                            "$defs": {
                                "MetaBidStrategy": {
                                    "description": "Meta Ads Bidding Strategies - Updated 2024+",
                                    "enum": [
                                        "LOWEST_COST_WITHOUT_CAP",
                                        "LOWEST_COST_WITH_BID_CAP",
                                        "LOWEST_COST_WITH_MIN_ROAS"
                                    ],
                                    "title": "MetaBidStrategy",
                                    "type": "string"
                                },
                                "MetaCampaignObjective": {
                                    "description": "Meta Ads Campaign Objectives - Updated to match Meta API 2024+",
                                    "enum": [
                                        "OUTCOME_AWARENESS",
                                        "OUTCOME_ENGAGEMENT",
                                        "OUTCOME_LEADS",
                                        "OUTCOME_SALES",
                                        "OUTCOME_TRAFFIC",
                                        "OUTCOME_APP_PROMOTION",
                                        "BRAND_AWARENESS",
                                        "LINK_CLICKS",
                                        "POST_ENGAGEMENT",
                                        "LEAD_GENERATION",
                                        "APP_INSTALLS",
                                        "CONVERSIONS"
                                    ],
                                    "title": "MetaCampaignObjective",
                                    "type": "string"
                                },
                                "MetaCampaignStatus": {
                                    "description": "Meta Ads Campaign Status",
                                    "enum": [
                                        "ACTIVE",
                                        "PAUSED",
                                        "DELETED",
                                        "ARCHIVED"
                                    ],
                                    "title": "MetaCampaignStatus",
                                    "type": "string"
                                }
                            },
                            "description": "Schema for creating a Meta Ads Campaign",
                            "properties": {
                                "name": {
                                    "description": "Campaign name",
                                    "title": "Name",
                                    "type": "string"
                                },
                                "objective": {
                                    "$ref": "#/$defs/MetaCampaignObjective",
                                    "description": "Campaign objective"
                                },
                                "status": {
                                    "$ref": "#/$defs/MetaCampaignStatus",
                                    "default": "PAUSED",
                                    "description": "Campaign status"
                                },
                                "buying_type": {
                                    "default": "AUCTION",
                                    "description": "Buying type for the campaign",
                                    "title": "Buying Type",
                                    "type": "string"
                                },
                                "bid_strategy": {
                                    "anyOf": [
                                        {
                                            "$ref": "#/$defs/MetaBidStrategy"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bidding strategy"
                                },
                                "daily_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Daily budget in cents",
                                    "title": "Daily Budget"
                                },
                                "lifetime_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Lifetime budget in cents",
                                    "title": "Lifetime Budget"
                                },
                                "start_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Campaign start time (ISO format)",
                                    "title": "Start Time"
                                },
                                "end_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Campaign end time (ISO format)",
                                    "title": "End Time"
                                },
                                "special_ad_categories": {
                                    "description": "Special ad categories (required by Meta API)",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Special Ad Categories",
                                    "type": "array"
                                }
                            },
                            "required": [
                                "name",
                                "objective"
                            ],
                            "title": "MetaCampaignCreate",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "update_meta_campaign",
                        "description": "Update an existing Meta Ads campaign with new configuration",
                        "input_schema": {
                            "$defs": {
                                "MetaBidStrategy": {
                                    "description": "Meta Ads Bidding Strategies - Updated 2024+",
                                    "enum": [
                                        "LOWEST_COST_WITHOUT_CAP",
                                        "LOWEST_COST_WITH_BID_CAP",
                                        "LOWEST_COST_WITH_MIN_ROAS"
                                    ],
                                    "title": "MetaBidStrategy",
                                    "type": "string"
                                },
                                "MetaCampaignStatus": {
                                    "description": "Meta Ads Campaign Status",
                                    "enum": [
                                        "ACTIVE",
                                        "PAUSED",
                                        "DELETED",
                                        "ARCHIVED"
                                    ],
                                    "title": "MetaCampaignStatus",
                                    "type": "string"
                                }
                            },
                            "description": "Schema for updating a Meta Ads Campaign",
                            "properties": {
                                "campaign_id": {
                                    "description": "Campaign ID to update",
                                    "title": "Campaign Id",
                                    "type": "string"
                                },
                                "name": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New campaign name",
                                    "title": "Name"
                                },
                                "status": {
                                    "anyOf": [
                                        {
                                            "$ref": "#/$defs/MetaCampaignStatus"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New campaign status"
                                },
                                "daily_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New daily budget in cents",
                                    "title": "Daily Budget"
                                },
                                "lifetime_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New lifetime budget in cents",
                                    "title": "Lifetime Budget"
                                },
                                "start_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New campaign start time (ISO format)",
                                    "title": "Start Time"
                                },
                                "end_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New campaign end time (ISO format)",
                                    "title": "End Time"
                                },
                                "bid_strategy": {
                                    "anyOf": [
                                        {
                                            "$ref": "#/$defs/MetaBidStrategy"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New bidding strategy"
                                },
                                "special_ad_categories": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "New special ad categories",
                                    "title": "Special Ad Categories"
                                }
                            },
                            "required": [
                                "campaign_id"
                            ],
                            "title": "MetaCampaignUpdate",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "update_campaign_status",
                        "description": "Update the status of a Meta Ads campaign (ACTIVE, PAUSED, etc.)",
                        "input_schema": {
                            "$defs": {
                                "MetaCampaignStatus": {
                                    "description": "Meta Ads Campaign Status",
                                    "enum": [
                                        "ACTIVE",
                                        "PAUSED",
                                        "DELETED",
                                        "ARCHIVED"
                                    ],
                                    "title": "MetaCampaignStatus",
                                    "type": "string"
                                }
                            },
                            "description": "Schema for updating campaign status only",
                            "properties": {
                                "campaign_id": {
                                    "description": "Campaign ID to update",
                                    "title": "Campaign Id",
                                    "type": "string"
                                },
                                "status": {
                                    "$ref": "#/$defs/MetaCampaignStatus",
                                    "description": "New campaign status"
                                }
                            },
                            "required": [
                                "campaign_id",
                                "status"
                            ],
                            "title": "MetaCampaignStatusUpdate",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_campaign_details",
                        "description": "Get detailed information about a Meta Ads campaign",
                        "input_schema": {
                            "description": "Schema for getting campaign details",
                            "properties": {
                                "campaign_id": {
                                    "description": "Campaign ID to retrieve",
                                    "title": "Campaign Id",
                                    "type": "string"
                                },
                                "fields": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Specific fields to retrieve",
                                    "title": "Fields"
                                }
                            },
                            "required": [
                                "campaign_id"
                            ],
                            "title": "MetaCampaignGet",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_ad_accounts",
                        "description": "Get ad accounts accessible by a user",
                        "input_schema": {
                            "description": "Schema for getting ad accounts",
                            "properties": {
                                "user_id": {
                                    "default": "me",
                                    "description": "Meta user ID or 'me' for the current user",
                                    "title": "User Id",
                                    "type": "string"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 10,
                                    "description": "Maximum number of accounts to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "GetAdAccountsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_account_info",
                        "description": "Get detailed information about a specific ad account",
                        "input_schema": {
                            "description": "Schema for getting account info",
                            "properties": {},
                            "title": "GetAccountInfoRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_account_pages",
                        "description": "Get pages associated with a Meta Ads account",
                        "input_schema": {
                            "description": "Schema for getting account pages",
                            "properties": {},
                            "title": "GetAccountPagesRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_campaigns",
                        "description": "Get campaigns for a Meta Ads account with optional filtering",
                        "input_schema": {
                            "description": "Schema for getting campaigns",
                            "properties": {
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 10,
                                    "description": "Maximum number of campaigns to return",
                                    "title": "Limit"
                                },
                                "status_filter": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Filter by status (empty for all, or 'ACTIVE', 'PAUSED', etc.)",
                                    "title": "Status Filter"
                                }
                            },
                            "title": "GetCampaignsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_campaign_details",
                        "description": "Get detailed information about a specific campaign",
                        "input_schema": {
                            "description": "Schema for getting campaign details (MCP version)",
                            "properties": {
                                "campaign_id": {
                                    "description": "Meta Ads campaign ID",
                                    "title": "Campaign Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "campaign_id"
                            ],
                            "title": "McpMetaAdsCampaignDetailsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_adsets",
                        "description": "Get ad sets for a Meta Ads account with optional filtering by campaign",
                        "input_schema": {
                            "description": "Schema for getting ad sets",
                            "properties": {
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 10,
                                    "description": "Maximum number of ad sets to return",
                                    "title": "Limit"
                                },
                                "campaign_id": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional campaign ID to filter by",
                                    "title": "Campaign Id"
                                }
                            },
                            "title": "GetAdSetsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_adset_details",
                        "description": "Get detailed information about a specific ad set",
                        "input_schema": {
                            "description": "Schema for getting ad set details",
                            "properties": {
                                "adset_id": {
                                    "description": "Meta Ads ad set ID",
                                    "title": "Adset Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "adset_id"
                            ],
                            "title": "GetAdSetDetailsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "create_adset",
                        "description": "Create a new ad set in a Meta Ads account",
                        "input_schema": {
                            "description": "Schema for creating an ad set",
                            "properties": {
                                "campaign_id": {
                                    "description": "Meta Ads campaign ID this ad set belongs to",
                                    "title": "Campaign Id",
                                    "type": "string"
                                },
                                "name": {
                                    "description": "Ad set name",
                                    "title": "Name",
                                    "type": "string"
                                },
                                "status": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "PAUSED",
                                    "description": "Initial ad set status",
                                    "title": "Status"
                                },
                                "daily_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Daily budget in cents",
                                    "title": "Daily Budget"
                                },
                                "lifetime_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Lifetime budget in cents",
                                    "title": "Lifetime Budget"
                                },
                                "countries": {
                                    "description": "List of target countries (e.g., ['US', 'CA'])",
                                    "items": {},
                                    "title": "Countries",
                                    "type": "array"
                                },
                                "publisher_platforms": {
                                    "description": "List of publisher platforms (e.g., ['facebook', 'instagram'])",
                                    "items": {},
                                    "title": "Publisher Platforms",
                                    "type": "array"
                                },
                                "facebook_positions": {
                                    "description": "List of Facebook positions (e.g., ['feed', 'right_hand_column'])",
                                    "items": {},
                                    "title": "Facebook Positions",
                                    "type": "array"
                                },
                                "optimization_goal": {
                                    "description": "Conversion optimization goal (e.g., 'LINK_CLICKS')",
                                    "title": "Optimization Goal",
                                    "type": "string"
                                },
                                "billing_event": {
                                    "description": "How you're charged (e.g., 'IMPRESSIONS')",
                                    "title": "Billing Event",
                                    "type": "string"
                                },
                                "bid_amount": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bid amount in account currency (in cents)",
                                    "title": "Bid Amount"
                                },
                                "bid_strategy": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bid strategy (e.g., 'LOWEST_COST')",
                                    "title": "Bid Strategy"
                                },
                                "start_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Start time (ISO 8601)",
                                    "title": "Start Time"
                                },
                                "end_time": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "End time (ISO 8601)",
                                    "title": "End Time"
                                }
                            },
                            "required": [
                                "campaign_id",
                                "name",
                                "countries",
                                "publisher_platforms",
                                "facebook_positions",
                                "optimization_goal",
                                "billing_event"
                            ],
                            "title": "CreateAdSetRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "update_adset",
                        "description": "Update an ad set with new settings including frequency caps",
                        "input_schema": {
                            "description": "Schema for updating an ad set",
                            "properties": {
                                "adset_id": {
                                    "description": "Meta Ads ad set ID",
                                    "title": "Adset Id",
                                    "type": "string"
                                },
                                "frequency_control_specs": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "additionalProperties": true,
                                                "type": "object"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "List of frequency control specifications",
                                    "title": "Frequency Control Specs"
                                },
                                "bid_strategy": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bid strategy (e.g., 'LOWEST_COST_WITH_BID_CAP')",
                                    "title": "Bid Strategy"
                                },
                                "bid_amount": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bid amount in account currency (in cents for USD)",
                                    "title": "Bid Amount"
                                },
                                "status": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Update ad set status (ACTIVE, PAUSED, etc.)",
                                    "title": "Status"
                                },
                                "targeting": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": true,
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Targeting specifications including targeting_automation",
                                    "title": "Targeting"
                                }
                            },
                            "required": [
                                "adset_id"
                            ],
                            "title": "UpdateAdSetRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_ads",
                        "description": "Get ads for a Meta Ads account with optional filtering",
                        "input_schema": {
                            "description": "Schema for getting ads",
                            "properties": {
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 10,
                                    "description": "Maximum number of ads to return",
                                    "title": "Limit"
                                },
                                "campaign_id": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional campaign ID to filter by",
                                    "title": "Campaign Id"
                                },
                                "adset_id": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional ad set ID to filter by",
                                    "title": "Adset Id"
                                }
                            },
                            "title": "GetAdsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "create_ad",
                        "description": "Create a new ad with an existing creative",
                        "input_schema": {
                            "description": "Schema for creating an ad",
                            "example": {
                                "adset_id": "<adset_id>",
                                "creative": {
                                    "creative_id": "<creative_id>"
                                },
                                "name": "Ad 1",
                                "status": "PAUSED"
                            },
                            "properties": {
                                "name": {
                                    "description": "Ad name",
                                    "title": "Name",
                                    "type": "string"
                                },
                                "adset_id": {
                                    "description": "Ad set ID where this ad will be placed",
                                    "title": "Adset Id",
                                    "type": "string"
                                },
                                "creative": {
                                    "additionalProperties": true,
                                    "description": "Creative object containing creative_id",
                                    "title": "Creative",
                                    "type": "object"
                                },
                                "status": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "PAUSED",
                                    "description": "Initial ad status",
                                    "title": "Status"
                                },
                                "bid_amount": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional bid amount (in cents)",
                                    "title": "Bid Amount"
                                },
                                "tracking_specs": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "additionalProperties": true,
                                                "type": "object"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional tracking specifications",
                                    "title": "Tracking Specs"
                                }
                            },
                            "required": [
                                "name",
                                "adset_id",
                                "creative"
                            ],
                            "title": "CreateAdRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_ad_details",
                        "description": "Get detailed information about a specific ad",
                        "input_schema": {
                            "description": "Schema for getting ad details",
                            "properties": {
                                "ad_id": {
                                    "description": "Meta Ads ad ID",
                                    "title": "Ad Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "ad_id"
                            ],
                            "title": "GetAdDetailsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "update_ad",
                        "description": "Update an ad with new settings",
                        "input_schema": {
                            "description": "Schema for updating an ad",
                            "properties": {
                                "ad_id": {
                                    "description": "Meta Ads ad ID",
                                    "title": "Ad Id",
                                    "type": "string"
                                },
                                "status": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Update ad status (ACTIVE, PAUSED, etc.)",
                                    "title": "Status"
                                },
                                "bid_amount": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Bid amount in account currency (in cents for USD)",
                                    "title": "Bid Amount"
                                }
                            },
                            "required": [
                                "ad_id"
                            ],
                            "title": "UpdateAdRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_ad_creatives",
                        "description": "Get creative details for a specific ad",
                        "input_schema": {
                            "description": "Schema for getting ad creatives",
                            "properties": {
                                "ad_id": {
                                    "description": "Meta Ads ad ID",
                                    "title": "Ad Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "ad_id"
                            ],
                            "title": "GetAdCreativesRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "upload_ad_image",
                        "description": "Upload an image to use in Meta Ads creatives",
                        "input_schema": {
                            "description": "Schema for uploading an ad image",
                            "properties": {
                                "image_url": {
                                    "description": "Image file URL for upload",
                                    "title": "Image Url",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "image_url"
                            ],
                            "title": "UploadAdImageRequest",
                            "type": "object"
                        },
                        "output_schema": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": "{\n  \"success\": true,\n  \"data\": {\n    \"image_hash\": \"9aaf3efc9ffaef5ce3bd7d89522323e0\"\n  }\n}"
                                }
                            ],
                            "isError": false
                        },
                        "annotations": null
                    },
                    {
                        "name": "get_ad_image",
                        "description": "Get, download, and visualize a Meta ad image in one step",
                        "input_schema": {
                            "description": "Schema for getting an ad image",
                            "properties": {
                                "ad_id": {
                                    "description": "Meta Ads ad ID",
                                    "title": "Ad Id",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "ad_id"
                            ],
                            "title": "GetAdImageRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_insights",
                        "description": "Get performance insights for a campaign, ad set, ad or account",
                        "input_schema": {
                            "description": "Schema for getting insights",
                            "properties": {
                                "object_id": {
                                    "description": "ID of the campaign, ad set, ad or account",
                                    "title": "Object Id",
                                    "type": "string"
                                },
                                "time_range": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "maximum",
                                    "description": "Time range for insights",
                                    "title": "Time Range"
                                },
                                "breakdown": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Optional breakdown dimension (e.g., age, gender, country)",
                                    "title": "Breakdown"
                                },
                                "level": {
                                    "description": "Level of aggregation (ad, adset, campaign, account)",
                                    "title": "Level",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "object_id",
                                "level"
                            ],
                            "title": "GetInsightsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "create_budget_schedule",
                        "description": "Create a budget schedule for a Meta Ads campaign",
                        "input_schema": {
                            "description": "Schema for creating a budget schedule",
                            "properties": {
                                "campaign_id": {
                                    "description": "Meta Ads campaign ID",
                                    "title": "Campaign Id",
                                    "type": "string"
                                },
                                "budget_value": {
                                    "description": "Amount of budget increase",
                                    "title": "Budget Value",
                                    "type": "integer"
                                },
                                "budget_value_type": {
                                    "description": "Type of budget value ('ABSOLUTE' or 'MULTIPLIER')",
                                    "title": "Budget Value Type",
                                    "type": "string"
                                },
                                "time_start": {
                                    "description": "Unix timestamp for when the high demand period should start",
                                    "title": "Time Start",
                                    "type": "integer"
                                },
                                "time_end": {
                                    "description": "Unix timestamp for when the high demand period should end",
                                    "title": "Time End",
                                    "type": "integer"
                                }
                            },
                            "required": [
                                "campaign_id",
                                "budget_value",
                                "budget_value_type",
                                "time_start",
                                "time_end"
                            ],
                            "title": "CreateBudgetScheduleRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_custom_audiences",
                        "description": "Get custom audiences including email lists, purchase data, and website visitor audiences",
                        "input_schema": {
                            "description": "Schema for getting custom audiences",
                            "properties": {
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of audiences to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "CustomAudiencesRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_lookalike_audiences",
                        "description": "Get lookalike audiences that are similar to your existing customers",
                        "input_schema": {
                            "description": "Schema for getting lookalike audiences",
                            "properties": {
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of audiences to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "LookalikeAudiencesRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_audience_insights",
                        "description": "Get detailed insights and analytics for a specific audience including size estimates and demographics",
                        "input_schema": {
                            "description": "Schema for getting audience insights",
                            "properties": {
                                "audience_id": {
                                    "description": "Audience ID to get insights for",
                                    "title": "Audience Id",
                                    "type": "string"
                                },
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "date_preset": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "last_30d",
                                    "description": "Date preset for insights (e.g., 'last_30d', 'last_7d', 'last_90d')",
                                    "title": "Date Preset"
                                }
                            },
                            "required": [
                                "audience_id"
                            ],
                            "title": "AudienceInsightsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_campaign_insights",
                        "description": "Get historical campaign performance data including customer engagement metrics and conversion data",
                        "input_schema": {
                            "description": "Schema for getting campaign insights with customer data",
                            "properties": {
                                "campaign_id": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Specific campaign ID (optional)",
                                    "title": "Campaign Id"
                                },
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "date_preset": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "last_30d",
                                    "description": "Date preset for insights",
                                    "title": "Date Preset"
                                },
                                "time_range": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": {
                                                "type": "string"
                                            },
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format",
                                    "title": "Time Range"
                                },
                                "level": {
                                    "default": "campaign",
                                    "description": "Level of insights (campaign, adset, ad)",
                                    "title": "Level",
                                    "type": "string"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of results to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "CampaignInsightsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_ad_account_insights",
                        "description": "Get comprehensive ad account insights including customer behavior and demographic breakdowns",
                        "input_schema": {
                            "description": "Schema for getting ad account insights",
                            "properties": {
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "date_preset": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "last_30d",
                                    "description": "Date preset for insights",
                                    "title": "Date Preset"
                                },
                                "time_range": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": {
                                                "type": "string"
                                            },
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format",
                                    "title": "Time Range"
                                },
                                "breakdown": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Breakdown dimensions (e.g., ['age', 'gender', 'country'])",
                                    "title": "Breakdown"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of results to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "AdAccountInsightsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_conversion_data",
                        "description": "Get detailed conversion tracking data to understand customer actions and purchase behavior",
                        "input_schema": {
                            "description": "Schema for getting conversion data",
                            "properties": {
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "date_preset": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "last_30d",
                                    "description": "Date preset for conversion data",
                                    "title": "Date Preset"
                                },
                                "time_range": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": {
                                                "type": "string"
                                            },
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format",
                                    "title": "Time Range"
                                },
                                "action_breakdown": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Action breakdown types",
                                    "title": "Action Breakdown"
                                },
                                "level": {
                                    "default": "account",
                                    "description": "Level of conversion data (account, campaign, adset, ad)",
                                    "title": "Level",
                                    "type": "string"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of results to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "ConversionDataRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_pixel_events",
                        "description": "Get Facebook Pixel events data to track customer interactions and website behavior",
                        "input_schema": {
                            "description": "Schema for getting pixel events data",
                            "properties": {
                                "pixel_id": {
                                    "description": "Facebook Pixel ID",
                                    "title": "Pixel Id",
                                    "type": "string"
                                },
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "date_preset": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": "last_30d",
                                    "description": "Date preset for pixel events",
                                    "title": "Date Preset"
                                },
                                "time_range": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": {
                                                "type": "string"
                                            },
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format",
                                    "title": "Time Range"
                                },
                                "breakdown": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Breakdown dimensions",
                                    "title": "Breakdown"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of results to return",
                                    "title": "Limit"
                                }
                            },
                            "required": [
                                "pixel_id"
                            ],
                            "title": "PixelEventsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_customer_segments",
                        "description": "Get customer segments and audience categorization data for targeted marketing",
                        "input_schema": {
                            "description": "Schema for getting customer segments",
                            "properties": {
                                "fields": {
                                    "description": "Fields to include in response",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "segment_type": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Type of segment to filter by",
                                    "title": "Segment Type"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of segments to return",
                                    "title": "Limit"
                                }
                            },
                            "title": "CustomerSegmentsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "get_audience_demographics",
                        "description": "Get detailed demographic information about your audiences including age, gender, location, and interests",
                        "input_schema": {
                            "description": "Schema for getting audience demographics",
                            "properties": {
                                "audience_id": {
                                    "description": "Audience ID to get demographics for",
                                    "title": "Audience Id",
                                    "type": "string"
                                },
                                "fields": {
                                    "description": "Demographic fields to include",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Fields",
                                    "type": "array"
                                },
                                "breakdown": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Breakdown dimensions for demographics",
                                    "title": "Breakdown"
                                },
                                "limit": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": 25,
                                    "description": "Maximum number of results to return",
                                    "title": "Limit"
                                }
                            },
                            "required": [
                                "audience_id"
                            ],
                            "title": "AudienceDemographicsRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "gather_ad_requirements",
                        "description": "Comprehensive tool to gather all requirements for creating Meta Ads campaigns, ad sets, and creatives",
                        "input_schema": {
                            "$defs": {
                                "MetaBidStrategy": {
                                    "description": "Meta Ads Bidding Strategies - Updated 2024+",
                                    "enum": [
                                        "LOWEST_COST_WITHOUT_CAP",
                                        "LOWEST_COST_WITH_BID_CAP",
                                        "LOWEST_COST_WITH_MIN_ROAS"
                                    ],
                                    "title": "MetaBidStrategy",
                                    "type": "string"
                                },
                                "MetaBillingEvent": {
                                    "description": "Meta Ads Billing Events",
                                    "enum": [
                                        "APP_INSTALLS",
                                        "CLICKS",
                                        "IMPRESSIONS",
                                        "LINK_CLICKS",
                                        "NONE",
                                        "OFFER_CLAIMS",
                                        "PAGE_LIKES",
                                        "POST_ENGAGEMENT",
                                        "THRUPLAY",
                                        "LANDING_PAGE_VIEWS",
                                        "PURCHASE",
                                        "LISTING_INTERACTION"
                                    ],
                                    "title": "MetaBillingEvent",
                                    "type": "string"
                                },
                                "MetaCallToActionType": {
                                    "description": "Meta Ads Call to Action Types",
                                    "enum": [
                                        "OPEN_LINK",
                                        "LIKE_PAGE",
                                        "SHOP_NOW",
                                        "PLAY_GAME",
                                        "INSTALL_APP",
                                        "USE_APP",
                                        "INSTALL_MOBILE_APP",
                                        "USE_MOBILE_APP",
                                        "BOOK_TRAVEL",
                                        "LISTEN_MUSIC",
                                        "WATCH_VIDEO",
                                        "LEARN_MORE",
                                        "SIGN_UP",
                                        "DOWNLOAD",
                                        "GET_QUOTE",
                                        "CONTACT_US",
                                        "APPLY_NOW",
                                        "BUY_NOW",
                                        "GET_OFFER",
                                        "GET_OFFER_VIEW",
                                        "BUY_TICKETS",
                                        "UPDATE_APP",
                                        "GET_DIRECTIONS",
                                        "BUY",
                                        "DONATE",
                                        "SUBSCRIBE",
                                        "SAY_THANKS",
                                        "SELL_NOW",
                                        "SHARE",
                                        "DONATE_NOW",
                                        "GET_STARTED",
                                        "VISIT_PAGES_FEED",
                                        "CALL_NOW",
                                        "EXPLORE_MORE",
                                        "CONFIRM",
                                        "JOIN_CHANNEL",
                                        "CALL",
                                        "REFER_FRIENDS",
                                        "MESSAGE_PAGE",
                                        "MOBILE_DOWNLOAD",
                                        "SAVE",
                                        "FOLLOW_PAGE",
                                        "WHATSAPP_MESSAGE",
                                        "FOLLOW_USER"
                                    ],
                                    "title": "MetaCallToActionType",
                                    "type": "string"
                                },
                                "MetaOptimizationGoal": {
                                    "description": "Meta Ads Optimization Goals",
                                    "enum": [
                                        "NONE",
                                        "APP_INSTALLS",
                                        "BRAND_AWARENESS",
                                        "CLICKS",
                                        "ENGAGED_USERS",
                                        "EVENT_RESPONSES",
                                        "IMPRESSIONS",
                                        "LEAD_GENERATION",
                                        "LINK_CLICKS",
                                        "OFFER_CLAIMS",
                                        "OFFSITE_CONVERSIONS",
                                        "PAGE_ENGAGEMENT",
                                        "PAGE_LIKES",
                                        "POST_ENGAGEMENT",
                                        "QUALITY_CALL",
                                        "REACH",
                                        "SOCIAL_IMPRESSIONS",
                                        "VIDEO_VIEWS",
                                        "THRUPLAY",
                                        "LANDING_PAGE_VIEWS",
                                        "VALUE",
                                        "CONVERSATIONS",
                                        "IN_APP_VALUE"
                                    ],
                                    "title": "MetaOptimizationGoal",
                                    "type": "string"
                                }
                            },
                            "description": "Comprehensive schema for gathering ad creation requirements",
                            "properties": {
                                "name": {
                                    "description": "Name of the product or brand",
                                    "title": "Name",
                                    "type": "string"
                                },
                                "objective": {
                                    "description": "Primary product or brand objective for the ad campaign",
                                    "title": "Objective",
                                    "type": "string"
                                },
                                "daily_budget": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "Daily budget in cents",
                                    "title": "Daily Budget"
                                },
                                "geo_locations_countries": {
                                    "description": "List of countries to target",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Geo Locations Countries",
                                    "type": "array"
                                },
                                "publisher_platforms": {
                                    "description": "List of platforms to target",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Publisher Platforms",
                                    "type": "array"
                                },
                                "facebook_positions": {
                                    "description": "List of positions to target on Facebook",
                                    "items": {
                                        "type": "string"
                                    },
                                    "title": "Facebook Positions",
                                    "type": "array"
                                },
                                "optimization_goal": {
                                    "$ref": "#/$defs/MetaOptimizationGoal",
                                    "description": "What to optimize for"
                                },
                                "billing_event": {
                                    "$ref": "#/$defs/MetaBillingEvent",
                                    "description": "What you're charged for"
                                },
                                "bid_strategy": {
                                    "$ref": "#/$defs/MetaBidStrategy",
                                    "description": "Bidding strategy"
                                },
                                "page_id": {
                                    "description": "Facebook page ID for the ad",
                                    "title": "Page Id",
                                    "type": "string"
                                },
                                "ad_text_message": {
                                    "description": "Main text/message of the ad",
                                    "title": "Ad Text Message",
                                    "type": "string"
                                },
                                "ad_headline": {
                                    "description": "Headline for the ad",
                                    "title": "Ad Headline",
                                    "type": "string"
                                },
                                "ad_description": {
                                    "description": "Description text for the ad",
                                    "title": "Ad Description",
                                    "type": "string"
                                },
                                "call_to_action": {
                                    "$ref": "#/$defs/MetaCallToActionType",
                                    "description": "Call to action button type"
                                },
                                "landing_page_url": {
                                    "description": "URL where users will be directed",
                                    "title": "Landing Page Url",
                                    "type": "string"
                                },
                                "image_url": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "URL of the image to use",
                                    "title": "Image Url"
                                }
                            },
                            "required": [
                                "name",
                                "objective",
                                "geo_locations_countries",
                                "publisher_platforms",
                                "facebook_positions",
                                "optimization_goal",
                                "billing_event",
                                "bid_strategy",
                                "page_id",
                                "ad_text_message",
                                "ad_headline",
                                "ad_description",
                                "call_to_action",
                                "landing_page_url"
                            ],
                            "title": "AdRequirementGatheringRequest",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "create_ad_creative",
                        "description": "Create a new ad creative using an uploaded image hash",
                        "input_schema": {
                            "description": "Schema for creating an ad creative",
                            "example": {
                                "call_to_action": {
                                    "type": "LEARN_MORE"
                                },
                                "description": "Sleek. Powerful. Easy to Use.",
                                "image_hash": "<uploaded_image_hash>",
                                "link": "<landing-page-link>",
                                "message": "This is the primary text for the ad. Discover why our new product is changing the game for everyone!",
                                "name": "The Best New Product is Here!",
                                "page_id": "<page_id>"
                            },
                            "properties": {
                                "name": {
                                    "description": "Creative name",
                                    "title": "Name",
                                    "type": "string"
                                },
                                "page_id": {
                                    "description": "Page ID for the ad creative",
                                    "title": "Page Id",
                                    "type": "string"
                                },
                                "image_hash": {
                                    "description": "Image hash for the ad creative",
                                    "title": "Image Hash",
                                    "type": "string"
                                },
                                "link": {
                                    "description": "Landing page URL for the ad creative",
                                    "title": "Link",
                                    "type": "string"
                                },
                                "message": {
                                    "description": "Primary text for the ad creative",
                                    "title": "Message",
                                    "type": "string"
                                },
                                "image_name": {
                                    "description": "Name of the image",
                                    "title": "Image Name",
                                    "type": "string"
                                },
                                "description": {
                                    "description": "Description of the ad creative",
                                    "title": "Description",
                                    "type": "string"
                                },
                                "call_to_action": {
                                    "additionalProperties": true,
                                    "description": "Call to action for the ad creative",
                                    "title": "Call To Action",
                                    "type": "object"
                                }
                            },
                            "required": [
                                "name",
                                "page_id",
                                "image_hash",
                                "link",
                                "message",
                                "image_name",
                                "description",
                                "call_to_action"
                            ],
                            "title": "CreateAdCreativeRequest",
                            "type": "object"
                        },
                        "output_schema": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"779030998020141\"\n  }\n}"
                                }
                            ],
                            "isError": false
                        },
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "social_media",
            "env_credential_status": "pending_input",
            "oauth_details": null
        },
        {
            "id": "a4dbc53f-af77-416f-9849-c82f0411695b",
            "name": "Webflow",
            "logo": null,
            "description": "Webflow Server",
            "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://server.smithery.ai/@webflow/mcp-server/mcp?profile=mechanical-snail-g1RifI&api_key=2bf9a5dd-cc32-424f-a0c3-cdc9031c3799",
                    "type": "streamable-http"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": [
                "webflow",
                "blogs",
                "blog publisher"
            ],
            "status": "active",
            "created_at": "2025-06-26T13:28:29.755749",
            "updated_at": "2025-07-10T07:53:15.635198",
            "image_name": null,
            "category": "general",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "collection_fields_create_option",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "id": {
                                            "type": "string"
                                        },
                                        "isEditable": {
                                            "type": "boolean"
                                        },
                                        "isRequired": {
                                            "type": "boolean"
                                        },
                                        "type": {
                                            "type": "string",
                                            "const": "Option"
                                        },
                                        "displayName": {
                                            "type": "string"
                                        },
                                        "helpText": {
                                            "type": "string"
                                        },
                                        "metadata": {
                                            "type": "object",
                                            "properties": {
                                                "options": {
                                                    "type": "array",
                                                    "items": {
                                                        "type": "object",
                                                        "properties": {
                                                            "name": {
                                                                "type": "string"
                                                            },
                                                            "id": {
                                                                "type": "string"
                                                            }
                                                        },
                                                        "required": [
                                                            "name"
                                                        ],
                                                        "additionalProperties": false
                                                    }
                                                }
                                            },
                                            "required": [
                                                "options"
                                            ],
                                            "additionalProperties": false
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "displayName",
                                        "metadata"
                                    ],
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collection_fields_create_reference",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "id": {
                                            "type": "string"
                                        },
                                        "isEditable": {
                                            "type": "boolean"
                                        },
                                        "isRequired": {
                                            "type": "boolean"
                                        },
                                        "type": {
                                            "type": "string",
                                            "enum": [
                                                "MultiReference",
                                                "Reference"
                                            ]
                                        },
                                        "displayName": {
                                            "type": "string"
                                        },
                                        "helpText": {
                                            "type": "string"
                                        },
                                        "metadata": {
                                            "type": "object",
                                            "properties": {
                                                "collectionId": {
                                                    "type": "string"
                                                }
                                            },
                                            "required": [
                                                "collectionId"
                                            ],
                                            "additionalProperties": false
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "displayName",
                                        "metadata"
                                    ],
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collection_fields_update",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "field_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "isRequired": {
                                            "type": "boolean"
                                        },
                                        "displayName": {
                                            "type": "string"
                                        },
                                        "helpText": {
                                            "type": "string"
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "field_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_create_item_live",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "items": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "string"
                                                    },
                                                    "cmsLocaleId": {
                                                        "type": "string"
                                                    },
                                                    "lastPublished": {
                                                        "type": "string"
                                                    },
                                                    "lastUpdated": {
                                                        "type": "string"
                                                    },
                                                    "createdOn": {
                                                        "type": "string"
                                                    },
                                                    "isArchived": {
                                                        "type": "boolean"
                                                    },
                                                    "isDraft": {
                                                        "type": "boolean"
                                                    },
                                                    "fieldData": {
                                                        "allOf": [
                                                            {
                                                                "type": "object",
                                                                "additionalProperties": {}
                                                            },
                                                            {
                                                                "type": "object",
                                                                "properties": {
                                                                    "name": {
                                                                        "type": "string"
                                                                    },
                                                                    "slug": {
                                                                        "type": "string"
                                                                    }
                                                                },
                                                                "required": [
                                                                    "name",
                                                                    "slug"
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "fieldData"
                                                ],
                                                "additionalProperties": false
                                            }
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_update_items_live",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "items": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "string"
                                                    },
                                                    "cmsLocaleId": {
                                                        "type": "string"
                                                    },
                                                    "lastPublished": {
                                                        "type": "string"
                                                    },
                                                    "lastUpdated": {
                                                        "type": "string"
                                                    },
                                                    "createdOn": {
                                                        "type": "string"
                                                    },
                                                    "isArchived": {
                                                        "type": "boolean"
                                                    },
                                                    "isDraft": {
                                                        "type": "boolean"
                                                    },
                                                    "fieldData": {
                                                        "allOf": [
                                                            {
                                                                "type": "object",
                                                                "additionalProperties": {}
                                                            },
                                                            {
                                                                "type": "object",
                                                                "properties": {
                                                                    "name": {
                                                                        "type": "string"
                                                                    },
                                                                    "slug": {
                                                                        "type": "string"
                                                                    }
                                                                }
                                                            }
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "id"
                                                ],
                                                "additionalProperties": false
                                            }
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_list_items",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "cmsLocaleId": {
                                    "type": "string"
                                },
                                "offset": {
                                    "type": "number"
                                },
                                "limit": {
                                    "type": "number"
                                },
                                "name": {
                                    "type": "string"
                                },
                                "slug": {
                                    "type": "string"
                                },
                                "sortBy": {
                                    "type": "string",
                                    "enum": [
                                        "lastPublished",
                                        "name",
                                        "slug"
                                    ]
                                },
                                "sortOrder": {
                                    "type": "string",
                                    "enum": [
                                        "asc",
                                        "desc"
                                    ]
                                }
                            },
                            "required": [
                                "collection_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_create_item",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "items": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "string"
                                                    },
                                                    "cmsLocaleId": {
                                                        "type": "string"
                                                    },
                                                    "lastPublished": {
                                                        "type": "string"
                                                    },
                                                    "lastUpdated": {
                                                        "type": "string"
                                                    },
                                                    "createdOn": {
                                                        "type": "string"
                                                    },
                                                    "isArchived": {
                                                        "type": "boolean"
                                                    },
                                                    "isDraft": {
                                                        "type": "boolean"
                                                    },
                                                    "fieldData": {
                                                        "allOf": [
                                                            {
                                                                "type": "object",
                                                                "additionalProperties": {}
                                                            },
                                                            {
                                                                "type": "object",
                                                                "properties": {
                                                                    "name": {
                                                                        "type": "string"
                                                                    },
                                                                    "slug": {
                                                                        "type": "string"
                                                                    }
                                                                },
                                                                "required": [
                                                                    "name",
                                                                    "slug"
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "fieldData"
                                                ],
                                                "additionalProperties": false
                                            }
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_update_items",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "items": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "string"
                                                    },
                                                    "cmsLocaleId": {
                                                        "type": "string"
                                                    },
                                                    "lastPublished": {
                                                        "type": "string"
                                                    },
                                                    "lastUpdated": {
                                                        "type": "string"
                                                    },
                                                    "createdOn": {
                                                        "type": "string"
                                                    },
                                                    "isArchived": {
                                                        "type": "boolean"
                                                    },
                                                    "isDraft": {
                                                        "type": "boolean"
                                                    },
                                                    "fieldData": {
                                                        "allOf": [
                                                            {
                                                                "type": "object",
                                                                "additionalProperties": {}
                                                            },
                                                            {
                                                                "type": "object",
                                                                "properties": {
                                                                    "name": {
                                                                        "type": "string"
                                                                    },
                                                                    "slug": {
                                                                        "type": "string"
                                                                    }
                                                                },
                                                                "required": [
                                                                    "name",
                                                                    "slug"
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "id",
                                                    "fieldData"
                                                ],
                                                "additionalProperties": false
                                            }
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_items_publish_items",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "itemIds": {
                                    "type": "array",
                                    "items": {
                                        "type": "string"
                                    }
                                }
                            },
                            "required": [
                                "collection_id",
                                "itemIds"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "sites_list",
                        "description": null,
                        "input_schema": {
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "sites_get",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "site_id": {
                                    "type": "string"
                                }
                            },
                            "required": [
                                "site_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": {
                            "properties": {
                                "sites": {
                                    "type": "array",
                                    "description": "List of Webflow sites",
                                    "title": "sites"
                                }
                            }
                        },
                        "annotations": null
                    },
                    {
                        "name": "sites_publish",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "site_id": {
                                    "type": "string"
                                },
                                "customDomains": {
                                    "type": "array",
                                    "items": {
                                        "type": "string"
                                    }
                                },
                                "publishToWebflowSubdomain": {
                                    "type": "boolean",
                                    "default": false
                                }
                            },
                            "required": [
                                "site_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "pages_list",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "site_id": {
                                    "type": "string"
                                },
                                "localeId": {
                                    "type": "string"
                                },
                                "limit": {
                                    "type": "number"
                                },
                                "offset": {
                                    "type": "number"
                                }
                            },
                            "required": [
                                "site_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "pages_get_metadata",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "page_id": {
                                    "type": "string"
                                },
                                "localeId": {
                                    "type": "string"
                                }
                            },
                            "required": [
                                "page_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "pages_update_page_settings",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "page_id": {
                                    "type": "string"
                                },
                                "localeId": {
                                    "type": "string"
                                },
                                "body": {
                                    "type": "object",
                                    "properties": {
                                        "id": {
                                            "type": "string"
                                        },
                                        "siteId": {
                                            "type": "string"
                                        },
                                        "title": {
                                            "type": "string"
                                        },
                                        "slug": {
                                            "type": "string"
                                        },
                                        "parentId": {
                                            "type": "string"
                                        },
                                        "collectionId": {
                                            "type": "string"
                                        },
                                        "createdOn": {
                                            "type": "string",
                                            "format": "date-time"
                                        },
                                        "lastUpdated": {
                                            "type": "string",
                                            "format": "date-time"
                                        },
                                        "archived": {
                                            "type": "boolean"
                                        },
                                        "draft": {
                                            "type": "boolean"
                                        },
                                        "canBranch": {
                                            "type": "boolean"
                                        },
                                        "isBranch": {
                                            "type": "boolean"
                                        },
                                        "isMembersOnly": {
                                            "type": "boolean"
                                        },
                                        "seo": {
                                            "type": "object",
                                            "properties": {
                                                "title": {
                                                    "type": "string"
                                                },
                                                "description": {
                                                    "type": "string"
                                                }
                                            },
                                            "additionalProperties": false
                                        },
                                        "openGraph": {
                                            "type": "object",
                                            "properties": {
                                                "title": {
                                                    "type": "string"
                                                },
                                                "titleCopied": {
                                                    "type": "boolean"
                                                },
                                                "description": {
                                                    "type": "string"
                                                },
                                                "descriptionCopied": {
                                                    "type": "boolean"
                                                }
                                            },
                                            "additionalProperties": false
                                        },
                                        "localeId": {
                                            "type": "string"
                                        },
                                        "publishedPath": {
                                            "type": "string"
                                        }
                                    },
                                    "required": [
                                        "id"
                                    ],
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "page_id",
                                "body"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "pages_get_content",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "page_id": {
                                    "type": "string"
                                },
                                "localeId": {
                                    "type": "string"
                                },
                                "limit": {
                                    "type": "number"
                                },
                                "offset": {
                                    "type": "number"
                                }
                            },
                            "required": [
                                "page_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "pages_update_static_content",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "page_id": {
                                    "type": "string"
                                },
                                "localeId": {
                                    "type": "string"
                                },
                                "nodes": {
                                    "type": "array",
                                    "items": {
                                        "anyOf": [
                                            {
                                                "type": "object",
                                                "properties": {
                                                    "nodeId": {
                                                        "type": "string"
                                                    },
                                                    "text": {
                                                        "type": "string"
                                                    }
                                                },
                                                "required": [
                                                    "nodeId",
                                                    "text"
                                                ],
                                                "additionalProperties": false
                                            },
                                            {
                                                "type": "object",
                                                "properties": {
                                                    "nodeId": {
                                                        "type": "string"
                                                    },
                                                    "propertyOverrides": {
                                                        "type": "array",
                                                        "items": {
                                                            "type": "object",
                                                            "properties": {
                                                                "propertyId": {
                                                                    "type": "string"
                                                                },
                                                                "text": {
                                                                    "type": "string"
                                                                }
                                                            },
                                                            "required": [
                                                                "propertyId",
                                                                "text"
                                                            ],
                                                            "additionalProperties": false
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "nodeId",
                                                    "propertyOverrides"
                                                ],
                                                "additionalProperties": false
                                            }
                                        ]
                                    }
                                }
                            },
                            "required": [
                                "page_id",
                                "localeId",
                                "nodes"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_list",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "site_id": {
                                    "type": "string"
                                }
                            },
                            "required": [
                                "site_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_get",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                }
                            },
                            "required": [
                                "collection_id"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collections_create",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "site_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "displayName": {
                                            "type": "string"
                                        },
                                        "singularName": {
                                            "type": "string"
                                        },
                                        "slug": {
                                            "type": "string"
                                        }
                                    },
                                    "required": [
                                        "displayName",
                                        "singularName"
                                    ],
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "site_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "collection_fields_create_static",
                        "description": null,
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "collection_id": {
                                    "type": "string"
                                },
                                "request": {
                                    "type": "object",
                                    "properties": {
                                        "id": {
                                            "type": "string"
                                        },
                                        "isEditable": {
                                            "type": "boolean"
                                        },
                                        "isRequired": {
                                            "type": "boolean"
                                        },
                                        "type": {
                                            "type": "string",
                                            "enum": [
                                                "Color",
                                                "DateTime",
                                                "Email",
                                                "File",
                                                "Image",
                                                "Link",
                                                "MultiImage",
                                                "Number",
                                                "Phone",
                                                "PlainText",
                                                "RichText",
                                                "Switch",
                                                "Video"
                                            ]
                                        },
                                        "displayName": {
                                            "type": "string"
                                        },
                                        "helpText": {
                                            "type": "string"
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "displayName"
                                    ],
                                    "additionalProperties": false
                                }
                            },
                            "required": [
                                "collection_id",
                                "request"
                            ],
                            "additionalProperties": false,
                            "$schema": "http://json-schema.org/draft-07/schema#"
                        },
                        "output_schema": null,
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "social_media",
            "env_credential_status": "pending_input",
            "oauth_details": null
        },
        {
            "id": "cde76df3-a879-496a-95f4-8b1f95d81a12",
            "name": "stock-image-generation-mcp",
            "logo": null,
            "description": "generate image and a stock image ",
            "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://stock-image-generation-mcp-dev-624209391722.us-central1.run.app/mcp",
                    "type": "streamable-http"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": [
                "image",
                "stock-image"
            ],
            "status": "active",
            "created_at": "2025-06-16T04:05:28.737966",
            "updated_at": "2025-06-27T13:08:13.357965",
            "image_name": null,
            "category": "marketing",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "generate_stock_image",
                        "description": "generate and find the stock image for the video",
                        "input_schema": {
                            "properties": {
                                "script": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Script",
                                    "type": "string"
                                },
                                "view_type": {
                                    "maxLength": 50,
                                    "minLength": 1,
                                    "title": "View Type",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "script",
                                "view_type"
                            ],
                            "title": "GenerateStockImage",
                            "type": "object"
                        },
                        "output_schema": {
                            "properties": {
                                "stock_image_clips": {
                                    "type": "array",
                                    "description": "List of stock video clips",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "at_time": {
                                                "type": "number",
                                                "description": "Time at which the image clip starts",
                                                "title": "at_time"
                                            },
                                            "url": {
                                                "type": "string",
                                                "description": "URL of the image",
                                                "title": "url"
                                            },
                                            "prompt": {
                                                "type": "string",
                                                "description": "prompt for the image",
                                                "title": "prompt"
                                            },
                                            "mimetype": {
                                                "type": "string",
                                                "description": "mimetype of the image clip",
                                                "title": "mimetype"
                                            }
                                        }
                                    },
                                    "title": "stock_image_clips"
                                }
                            }
                        },
                        "annotations": null
                    },
                    {
                        "name": "generate_ai_stock_image",
                        "description": "generate and find the stock image for the video",
                        "input_schema": {
                            "properties": {
                                "script": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Script",
                                    "type": "string"
                                },
                                "view_type": {
                                    "maxLength": 50,
                                    "minLength": 1,
                                    "title": "View Type",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "script",
                                "view_type"
                            ],
                            "title": "GenerateAIStockImage",
                            "type": "object"
                        },
                        "output_schema": {
                            "type": "object",
                            "properties": {
                                "stock_image_clips": {
                                    "type": "array",
                                    "description": "stock_image_clips",
                                    "title": "stock_image_clips"
                                }
                            },
                            "required": [
                                "stock_image_clips"
                            ]
                        },
                        "annotations": null
                    },
                    {
                        "name": "generate_image",
                        "description": "generate the image using the script",
                        "input_schema": {
                            "properties": {
                                "prompt": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Prompt",
                                    "type": "string"
                                },
                                "view_type": {
                                    "maxLength": 50,
                                    "minLength": 1,
                                    "title": "View Type",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "prompt",
                                "view_type"
                            ],
                            "title": "GenerateImage",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "fetch_stock_images",
                        "description": "fetch the stock image using the script",
                        "input_schema": {
                            "properties": {
                                "script": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Script",
                                    "type": "string"
                                },
                                "view_type": {
                                    "maxLength": 50,
                                    "minLength": 1,
                                    "title": "View Type",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "script",
                                "view_type"
                            ],
                            "title": "GenerateAIStockImage",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "social_media",
            "env_credential_status": "pending_input",
            "oauth_details": null
        },
        {
            "id": "de313c10-d664-49e0-889e-e41e0230f704",
            "name": "stock-video-generation-mcp",
            "logo": null,
            "description": "stock video generation ",
            "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://stock-video-generation-mcp-dev-624209391722.us-central1.run.app/mcp",
                    "type": "streamable-http"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": [
                "stockvideo"
            ],
            "status": "active",
            "created_at": "2025-06-15T13:34:20.621682",
            "updated_at": "2025-07-09T09:16:04.456371",
            "image_name": null,
            "category": "marketing",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "generate_stock_video",
                        "description": "generate and find the stock video for the video",
                        "input_schema": {
                            "properties": {
                                "script": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Script",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "script"
                            ],
                            "title": "GenerateStockVideo",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "generate_ai_stock_video",
                        "description": "generate the ai stock video using the script",
                        "input_schema": {
                            "properties": {
                                "script": {
                                    "description": "Script is required",
                                    "maxLength": 1000,
                                    "minLength": 1,
                                    "title": "Script",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "script"
                            ],
                            "title": "GenerateAIStockVideo",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    },
                    {
                        "name": "fetch_stock_videos",
                        "description": "fetch the stock videos from search terms",
                        "input_schema": {
                            "properties": {
                                "search_terms": {
                                    "description": "Search terms are required",
                                    "items": {
                                        "maxLength": 100,
                                        "minLength": 1,
                                        "type": "string"
                                    },
                                    "title": "Search Terms",
                                    "type": "array"
                                },
                                "page": {
                                    "default": 1,
                                    "description": "Page number for pagination, default is 1",
                                    "minimum": 1,
                                    "title": "Page",
                                    "type": "integer"
                                },
                                "page_size": {
                                    "default": 10,
                                    "description": "Number of results per page, default is 10",
                                    "minimum": 1,
                                    "title": "Page Size",
                                    "type": "integer"
                                }
                            },
                            "required": [
                                "search_terms"
                            ],
                            "title": "FetchStockVideos",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "social_media",
            "env_credential_status": "pending_input",
            "oauth_details": null
        },
        {
            "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
            "name": "script-generation-mcp",
            "logo": null,
            "description": "generate video script",
            "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077",
            "user_ids": null,
            "owner_type": "user",
            "config": [
                {
                    "url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/mcp",
                    "type": "streamable-http"
                },
                {
                    "url": "https://script-generation-mcp-dev-624209391722.us-central1.run.app/sse",
                    "type": "sse"
                }
            ],
            "git_url": null,
            "git_branch": null,
            "deployment_status": "pending",
            "visibility": "public",
            "tags": [
                "video",
                "auomation",
                "script"
            ],
            "status": "active",
            "created_at": "2025-06-15T10:38:35.635904",
            "updated_at": "2025-07-11T10:47:56.678712",
            "image_name": null,
            "category": "marketing",
            "mcp_tools_config": {
                "meta": null,
                "nextCursor": null,
                "tools": [
                    {
                        "name": "script_generate",
                        "description": "Provide topic and keyword to generator Script",
                        "input_schema": {
                            "$defs": {
                                "Keywords": {
                                    "properties": {
                                        "time": {
                                            "anyOf": [
                                                {
                                                    "type": "string"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Time"
                                        },
                                        "objective": {
                                            "anyOf": [
                                                {
                                                    "type": "string"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Objective"
                                        },
                                        "audience": {
                                            "anyOf": [
                                                {
                                                    "type": "string"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Audience"
                                        },
                                        "gender": {
                                            "anyOf": [
                                                {
                                                    "type": "string"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Gender"
                                        },
                                        "tone": {
                                            "anyOf": [
                                                {
                                                    "type": "string"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Tone"
                                        },
                                        "speakers": {
                                            "anyOf": [
                                                {
                                                    "items": {
                                                        "type": "string"
                                                    },
                                                    "type": "array"
                                                },
                                                {
                                                    "type": "null"
                                                }
                                            ],
                                            "default": null,
                                            "title": "Speakers"
                                        }
                                    },
                                    "title": "Keywords",
                                    "type": "object"
                                },
                                "ScriptType": {
                                    "enum": [
                                        "VIDEO",
                                        "TOPIC",
                                        "SCRIPT",
                                        "BLOG",
                                        "AI"
                                    ],
                                    "title": "ScriptType",
                                    "type": "string"
                                },
                                "VideoType": {
                                    "enum": [
                                        "SHORT",
                                        "LONG"
                                    ],
                                    "title": "VideoType",
                                    "type": "string"
                                }
                            },
                            "properties": {
                                "topic": {
                                    "title": "Topic",
                                    "type": "string"
                                },
                                "script_type": {
                                    "$ref": "#/$defs/ScriptType",
                                    "default": "TOPIC"
                                },
                                "keywords": {
                                    "$ref": "#/$defs/Keywords"
                                },
                                "video_type": {
                                    "$ref": "#/$defs/VideoType",
                                    "default": "SHORT"
                                },
                                "link": {
                                    "anyOf": [
                                        {
                                            "format": "uri",
                                            "maxLength": 2083,
                                            "minLength": 1,
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "title": "Link"
                                }
                            },
                            "required": [
                                "topic"
                            ],
                            "title": "GenerateScriptInput",
                            "type": "object"
                        },
                        "output_schema": {
                            "properties": {
                                "title": {
                                    "type": "string",
                                    "description": "Title of the generated script",
                                    "title": "title"
                                },
                                "script": {
                                    "type": "string",
                                    "description": "The generated script",
                                    "title": "script"
                                },
                                "script_type": {
                                    "type": "string",
                                    "description": "Type of the script",
                                    "title": "script_type"
                                },
                                "video_type": {
                                    "type": "string",
                                    "description": "The type of video",
                                    "title": "video_type"
                                },
                                "link": {
                                    "type": "string",
                                    "format": "uri",
                                    "description": "Optional link for the script",
                                    "title": "link"
                                }
                            }
                        },
                        "annotations": null
                    },
                    {
                        "name": "research",
                        "description": "Research for the given topic",
                        "input_schema": {
                            "properties": {
                                "topic": {
                                    "title": "Topic",
                                    "type": "string"
                                }
                            },
                            "required": [
                                "topic"
                            ],
                            "title": "ResearchInput",
                            "type": "object"
                        },
                        "output_schema": null,
                        "annotations": null
                    }
                ]
            },
            "is_added": true,
            "env_keys": null,
            "component_category": "social_media",
            "env_credential_status": "pending_input",
            "oauth_details": null
        }
    ],
    "metadata": {
        "total": 5,
        "totalPages": 1,
        "currentPage": 1,
        "pageSize": 10,
        "hasNextPage": false,
        "hasPreviousPage": false
    }
}