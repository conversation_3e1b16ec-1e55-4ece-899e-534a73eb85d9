Updating node AgenticAI-1753331604248 with new config for system_message: greet ${{name}} with  salutation as dear 
inputVisibility.ts:51 ✅ [CACHE-HIT] model_provider - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] base_url - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] model_name - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] temperature - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] description - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] execution_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] query - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] system_message - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] termination_condition - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] max_tokens - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] input_variables - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] tools - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] memory - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] autogen_agent_type - OPTIMIZED VERSION WORKING
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: model_provider (Model Provider)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field model_provider required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'model_provider'}
InputRenderer.tsx:228 🔍 Model provider dropdown options: (57) ['01-ai', 'aetherwiing', 'agentica-org', 'ai21', 'aion-labs', 'alfredpros', 'alpindale', 'amazon', 'anthracite-org', 'anthropic', 'arcee-ai', 'arliai', 'baidu', 'cognitivecomputations', 'cohere', 'deepseek', 'eleutherai', 'eva-unit-01', 'featherless', 'google', 'gryphe', 'inception', 'infermatic', 'inflection', 'liquid', 'mancer', 'meta-llama', 'microsoft', 'minimax', 'mistralai', 'moonshotai', 'morph', 'neversleep', 'nothingiisreal', 'nousresearch', 'nvidia', 'openai', 'opengvlab', 'openrouter', 'perplexity', 'pygmalionai', 'qwen', 'raifle', 'rekaai', 'sao10k', 'sarvamai', 'scb10x', 'shisa-ai', 'sophosympatheia', 'switchpoint', 'tencent', 'thedrummer', 'thudm', 'tngtech', 'undi95', 'x-ai', 'Custom']
InputRenderer.tsx:228 🔍 Model provider dropdown options: (57) ['01-ai', 'aetherwiing', 'agentica-org', 'ai21', 'aion-labs', 'alfredpros', 'alpindale', 'amazon', 'anthracite-org', 'anthropic', 'arcee-ai', 'arliai', 'baidu', 'cognitivecomputations', 'cohere', 'deepseek', 'eleutherai', 'eva-unit-01', 'featherless', 'google', 'gryphe', 'inception', 'infermatic', 'inflection', 'liquid', 'mancer', 'meta-llama', 'microsoft', 'minimax', 'mistralai', 'moonshotai', 'morph', 'neversleep', 'nothingiisreal', 'nousresearch', 'nvidia', 'openai', 'opengvlab', 'openrouter', 'perplexity', 'pygmalionai', 'qwen', 'raifle', 'rekaai', 'sao10k', 'sarvamai', 'scb10x', 'shisa-ai', 'sophosympatheia', 'switchpoint', 'tencent', 'thedrummer', 'thudm', 'tngtech', 'undi95', 'x-ai', 'Custom']
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: model_name (Model)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field model_name required status: OPTIONAL (required !== false: false)
InputRenderer.tsx:188 🔍 Model dropdown check: {name: 'model_name', hasDynamicFiltering: true, inputDef: {…}}
InputRenderer.tsx:27 🔍 Getting selected provider for node: AgenticAI-1753331604248 Provider: openai
InputRenderer.tsx:199 🔍 Model dropdown data: {selectedProvider: 'openai', providerIdMapping: {…}, allModelsCount: 0, value: 'gpt-4o-mini'}
InputRenderer.tsx:188 🔍 Model dropdown check: {name: 'model_name', hasDynamicFiltering: true, inputDef: {…}}
InputRenderer.tsx:27 🔍 Getting selected provider for node: AgenticAI-1753331604248 Provider: openai
InputRenderer.tsx:199 🔍 Model dropdown data: {selectedProvider: 'openai', providerIdMapping: {…}, allModelsCount: 0, value: 'gpt-4o-mini'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: temperature (Temperature)
    - Input type: float
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field temperature required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'temperature'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: description (Description)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field description required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'description'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: execution_type (Execution Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field execution_type required status: OPTIONAL (required !== false: false)
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'execution_type', inputType: 'dropdown', optionsCount: 2, value: 'response'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'execution_type', inputType: 'dropdown', optionsCount: 2, value: 'response'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: query (Query/Objective)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:680 [2025-07-24 04:52:01] [isFieldRequired] Field query is a handle input but NOT connected, required for direct input
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: system_message (System Message)
    - Input type: multiline
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 04:52:01] [isFieldRequired] Field system_message is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'multiline', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'system_message'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: max_tokens (Max Tokens)
    - Input type: int
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field max_tokens required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'max_tokens'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: input_variables (Input Variables)
    - Input type: dict
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: YES
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 04:52:01] [isFieldRequired] Field input_variables is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'input_variables'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: tools (Tools)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 04:52:01] [isFieldRequired] Field tools is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1753331604248', inputName: 'tools'}
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: memory (Memory Object)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 04:52:01] [isFieldRequired] Field memory is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:621 [2025-07-24 04:52:01] [isFieldRequired] Checking if field is required (cache miss):
    - Node: AgenticAI-1753331604248 (AI Agent Executor)
    - Field: autogen_agent_type (AutoGen Agent Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:574 [2025-07-24 04:52:01] [isMCPMarketplaceComponent] Node AgenticAI-1753331604248 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: undefined
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:728 [2025-07-24 04:52:01] [isFieldRequired] Standard component field autogen_agent_type required status: OPTIONAL (required !== false: false)
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'autogen_agent_type', inputType: 'dropdown', optionsCount: 3, value: 'Assistant'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'autogen_agent_type', inputType: 'dropdown', optionsCount: 3, value: 'Assistant'}
Sidebar.tsx:473 Sidebar component categories: (13) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (13) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] model_provider - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] base_url - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] model_name - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] temperature - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] description - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] execution_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] query - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] system_message - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] termination_condition - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] max_tokens - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] input_variables - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] tools - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] memory - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] autogen_agent_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] model_provider - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] base_url - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] model_name - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] temperature - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] description - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] execution_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] query - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] system_message - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] termination_condition - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] max_tokens - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] input_variables - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] tools - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] memory - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] autogen_agent_type - OPTIMIZED VERSION WORKING
InputRenderer.tsx:228 🔍 Model provider dropdown options: (57) ['01-ai', 'aetherwiing', 'agentica-org', 'ai21', 'aion-labs', 'alfredpros', 'alpindale', 'amazon', 'anthracite-org', 'anthropic', 'arcee-ai', 'arliai', 'baidu', 'cognitivecomputations', 'cohere', 'deepseek', 'eleutherai', 'eva-unit-01', 'featherless', 'google', 'gryphe', 'inception', 'infermatic', 'inflection', 'liquid', 'mancer', 'meta-llama', 'microsoft', 'minimax', 'mistralai', 'moonshotai', 'morph', 'neversleep', 'nothingiisreal', 'nousresearch', 'nvidia', 'openai', 'opengvlab', 'openrouter', 'perplexity', 'pygmalionai', 'qwen', 'raifle', 'rekaai', 'sao10k', 'sarvamai', 'scb10x', 'shisa-ai', 'sophosympatheia', 'switchpoint', 'tencent', 'thedrummer', 'thudm', 'tngtech', 'undi95', 'x-ai', 'Custom']
InputRenderer.tsx:228 🔍 Model provider dropdown options: (57) ['01-ai', 'aetherwiing', 'agentica-org', 'ai21', 'aion-labs', 'alfredpros', 'alpindale', 'amazon', 'anthracite-org', 'anthropic', 'arcee-ai', 'arliai', 'baidu', 'cognitivecomputations', 'cohere', 'deepseek', 'eleutherai', 'eva-unit-01', 'featherless', 'google', 'gryphe', 'inception', 'infermatic', 'inflection', 'liquid', 'mancer', 'meta-llama', 'microsoft', 'minimax', 'mistralai', 'moonshotai', 'morph', 'neversleep', 'nothingiisreal', 'nousresearch', 'nvidia', 'openai', 'opengvlab', 'openrouter', 'perplexity', 'pygmalionai', 'qwen', 'raifle', 'rekaai', 'sao10k', 'sarvamai', 'scb10x', 'shisa-ai', 'sophosympatheia', 'switchpoint', 'tencent', 'thedrummer', 'thudm', 'tngtech', 'undi95', 'x-ai', 'Custom']
InputRenderer.tsx:188 🔍 Model dropdown check: {name: 'model_name', hasDynamicFiltering: true, inputDef: {…}}
InputRenderer.tsx:27 🔍 Getting selected provider for node: AgenticAI-1753331604248 Provider: openai
InputRenderer.tsx:199 🔍 Model dropdown data: {selectedProvider: 'openai', providerIdMapping: {…}, allModelsCount: 0, value: 'gpt-4o-mini'}
InputRenderer.tsx:188 🔍 Model dropdown check: {name: 'model_name', hasDynamicFiltering: true, inputDef: {…}}
InputRenderer.tsx:27 🔍 Getting selected provider for node: AgenticAI-1753331604248 Provider: openai
InputRenderer.tsx:199 🔍 Model dropdown data: {selectedProvider: 'openai', providerIdMapping: {…}, allModelsCount: 0, value: 'gpt-4o-mini'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'execution_type', inputType: 'dropdown', optionsCount: 2, value: 'response'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'execution_type', inputType: 'dropdown', optionsCount: 2, value: 'response'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'autogen_agent_type', inputType: 'dropdown', optionsCount: 3, value: 'Assistant'}
InputRenderer.tsx:259 🔍 Regular dropdown detected: {name: 'autogen_agent_type', inputType: 'dropdown', optionsCount: 3, value: 'Assistant'}